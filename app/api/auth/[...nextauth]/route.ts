import NextAuth from "next-auth";
import Google from "next-auth/providers/google";
import { handleCallbackProvider, handleLoginSuccess } from "../_helpers";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

const authOptions = {
  session: { strategy: "jwt" as const },
  providers: [
    Google({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,

      checks: ["pkce", "state", "nonce"],
      authorization: {
        params: {
          scope: "openid email profile",
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ account, profile, user }: any) {
      const accessOrIdToken = account?.id_token;
      if (!accessOrIdToken) {
        return `/auth/error?error=NoAccessToken`;
      }

      try {
        const decoded: any = jwtDecode(accessOrIdToken);
        const idTokenNonce: string | undefined = decoded?.nonce;
        const cookieStore = await cookies();
        const expectedNonce = cookieStore.get("turnkey_login_nonce")?.value;

        if (!idTokenNonce || !expectedNonce || idTokenNonce !== expectedNonce) {
          return `/auth/error?error=InvalidNonce`;
        }
      } catch (e) {
        return `/auth/error?error=InvalidIdToken`;
      }

      try {
        const provider = account?.provider || "google";
        const email = profile?.email || user?.email || null;

        const cookieStore = await cookies();
        const publicKey = cookieStore.get("turnkey_login_public_key")?.value;

        const result: any = await handleCallbackProvider(
          accessOrIdToken,
          provider,
          email,
          publicKey
        );
        await handleLoginSuccess(result?.jwtToken);

        try {
          const responseCookies = await cookies();
          responseCookies.set("turnkey_login_nonce", "", {
            httpOnly: false,
            secure: true,
            sameSite: "lax",
            path: "/",
            expires: new Date(0),
          } as any);
          responseCookies.set("turnkey_login_public_key", "", {
            httpOnly: false,
            secure: true,
            sameSite: "lax",
            path: "/",
            expires: new Date(0),
          } as any);
        } catch {}

        return true;
      } catch (error: any) {
        return `/auth/error?error=${encodeURIComponent(
          error?.message || "BackendAuthFailed"
        )}`;
      }
    },
    async jwt({ token, account }: any) {
      if (account?.id_token) token.googleIdToken = account.id_token;
      return token;
    },
    async session({ session, token }: any) {
      (session.user as any) = session.user || {};
      (session.user as any).googleIdToken = token.googleIdToken;
      return session;
    },
    async redirect({ url, baseUrl }: any) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return `${baseUrl}/auth/finalize`;
    },
  },
};

const handler = NextAuth(authOptions as any);
export { handler as GET, handler as POST };
