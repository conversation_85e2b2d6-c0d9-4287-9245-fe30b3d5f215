import rf from "@/services/RequestFactory";
import { COOKIES_ACCESS_TOKEN_KEY, JWT_LIVE_TIME } from "@/constants/common";

/**
 * Exchange a provider token (Google access_token or id_token) for our app JWT
 */
export async function handleCallbackProvider(
  token: string,
  provider: string,
  email?: string | null,
  publicKey?: string
): Promise<{ jwtToken: string }> {
  // Prefer sending both keys so backend can accept either and include email if available
  const payload: any = {
    oauthProvider: { oidcToken: token, providerName: provider },
  };
  if (email) payload.email = email;
  if (publicKey) payload.publicKey = publicKey;

  const res: any = await rf
    .getRequest("TurnkeyAuthRequest")
    .exchangeTurnkeySession(payload);

  if (!res?.jwtToken) {
    throw new Error("Backend did not return a jwtToken");
  }

  return { jwtToken: res.jwtToken };
}

/**
 * Handle successful login: set auth cookie for SSR while client sets Redux/localStorage
 */
export async function handleLoginSuccess(jwtToken?: string) {
  if (!jwtToken) throw new Error("Missing jwtToken");

  try {
    const { cookies } = await import("next/headers");
    const cookieStore = await cookies();
    cookieStore.set({
      name: COOKIES_ACCESS_TOKEN_KEY,
      value: jwtToken,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);
  } catch {
    // Ignore; client-side flow (Redux/localStorage) will still complete after redirect
  }

  return { jwtToken };
}
